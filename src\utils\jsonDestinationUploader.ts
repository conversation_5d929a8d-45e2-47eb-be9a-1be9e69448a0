// src/utils/jsonDestinationUploader.ts
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Destination } from '@/types/firebase';

interface JsonDestinationData {
  name: string;
  description: string;
  country: string;
  region: string;
  coordinates: { lat: number; lng: number };
  bestTimeToVisit: string[];
  climate: string;
  wildlife: Array<{
    species: string;
    scientificName: string;
    category: string;
    abundance: string;
    bestSpottingTime: string;
    behavior: string;
    conservationStatus: string;
    photographyTips: string;
  }>;
  images: string[];
  activities: string[];
  accommodations: string[];
  featured: boolean;
  detailedGuide: {
    overview: string;
    geography: string;
    history: string;
    bestTimeToVisit: {
      drySeason: string;
      greenSeason: string;
      photography: string;
      birding: string;
    };
    gettingThere: string;
    accommodation: string;
    packingTips: string[];
    healthSafety: string;
    travelTips: string[];
  };
  seasonalInfo: {
    drySeason: {
      months: string[];
      description: string;
      wildlife: string;
      photography: string;
      advantages: string[];
      disadvantages: string[];
    };
    greenSeason: {
      months: string[];
      description: string;
      wildlife: string;
      photography: string;
      advantages: string[];
      disadvantages: string[];
    };
  };
  conservationInfo: {
    initiatives: string[];
    challenges: string[];
    howTouristsHelp: string[];
    conservationFee: number;
  };
  culturalInfo: {
    tribes: string[];
    languages: string[];
    traditions: string[];
    etiquette: string[];
    culturalSites: string[];
  };
}

export class JsonDestinationUploader {
  static validateDestinationData(destinationData: any): destinationData is JsonDestinationData {
    const requiredFields = [
      'name', 'description', 'country', 'region', 'coordinates',
      'bestTimeToVisit', 'climate', 'wildlife', 'images', 'activities',
      'accommodations', 'featured', 'detailedGuide', 'conservationInfo', 'culturalInfo'
    ];

    for (const field of requiredFields) {
      if (!(field in destinationData)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate coordinates object
    if (!destinationData.coordinates || typeof destinationData.coordinates !== 'object' ||
        typeof destinationData.coordinates.lat !== 'number' ||
        typeof destinationData.coordinates.lng !== 'number') {
      throw new Error('coordinates must be an object with lat and lng numbers');
    }

    // Validate arrays
    const arrayFields = [
      'bestTimeToVisit', 'wildlife', 'images', 'activities', 'accommodations'
    ];
    for (const field of arrayFields) {
      if (destinationData[field] && !Array.isArray(destinationData[field])) {
        throw new Error(`Field ${field} must be an array`);
      }
    }

    // Validate nested objects
    if (!destinationData.detailedGuide || typeof destinationData.detailedGuide !== 'object') {
      throw new Error('Missing or invalid detailedGuide object');
    }

    if (!destinationData.conservationInfo || typeof destinationData.conservationInfo !== 'object') {
      throw new Error('Missing or invalid conservationInfo object');
    }

    if (!destinationData.culturalInfo || typeof destinationData.culturalInfo !== 'object') {
      throw new Error('Missing or invalid culturalInfo object');
    }

    // Validate wildlife array structure
    if (destinationData.wildlife && Array.isArray(destinationData.wildlife)) {
      for (const animal of destinationData.wildlife) {
        if (!animal || typeof animal !== 'object') {
          throw new Error('Each wildlife entry must be an object');
        }
        const requiredWildlifeFields = [
          'species', 'scientificName', 'category', 'abundance',
          'bestSpottingTime', 'behavior', 'conservationStatus', 'photographyTips'
        ];
        for (const field of requiredWildlifeFields) {
          if (!(field in animal)) {
            throw new Error(`Missing required wildlife field: ${field}`);
          }
        }
      }
    }

    return true;
  }

  static async uploadSingleDestination(destinationData: JsonDestinationData): Promise<string> {
    try {
      // Validate the destination data
      this.validateDestinationData(destinationData);

      // Prepare the destination data for Firestore
      const firestoreDestinationData = {
        ...destinationData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        // Ensure all required fields have defaults
        wildlife: destinationData.wildlife || [],
        images: destinationData.images || [],
        activities: destinationData.activities || [],
        accommodations: destinationData.accommodations || [],
        bestTimeToVisit: destinationData.bestTimeToVisit || [],
        detailedGuide: {
          overview: destinationData.detailedGuide?.overview || '',
          geography: destinationData.detailedGuide?.geography || '',
          history: destinationData.detailedGuide?.history || '',
          bestTimeToVisit: destinationData.detailedGuide?.bestTimeToVisit || {
            drySeason: '',
            greenSeason: '',
            photography: '',
            birding: ''
          },
          gettingThere: destinationData.detailedGuide?.gettingThere || '',
          accommodation: destinationData.detailedGuide?.accommodation || '',
          packingTips: destinationData.detailedGuide?.packingTips || [],
          healthSafety: destinationData.detailedGuide?.healthSafety || '',
          travelTips: destinationData.detailedGuide?.travelTips || []
        },
        seasonalInfo: destinationData.seasonalInfo || {
          drySeason: {
            months: [],
            description: '',
            wildlife: '',
            photography: '',
            advantages: [],
            disadvantages: []
          },
          greenSeason: {
            months: [],
            description: '',
            wildlife: '',
            photography: '',
            advantages: [],
            disadvantages: []
          }
        },
        conservationInfo: {
          initiatives: destinationData.conservationInfo?.initiatives || [],
          challenges: destinationData.conservationInfo?.challenges || [],
          howTouristsHelp: destinationData.conservationInfo?.howTouristsHelp || [],
          conservationFee: destinationData.conservationInfo?.conservationFee || 0
        },
        culturalInfo: {
          tribes: destinationData.culturalInfo?.tribes || [],
          languages: destinationData.culturalInfo?.languages || [],
          traditions: destinationData.culturalInfo?.traditions || [],
          etiquette: destinationData.culturalInfo?.etiquette || [],
          culturalSites: destinationData.culturalInfo?.culturalSites || []
        }
      };

      // Upload to Firestore
      const docRef = await addDoc(collection(db, 'destinations'), firestoreDestinationData);
      return docRef.id;
    } catch (error) {
      console.error('Error uploading destination:', error);
      throw error;
    }
  }

  static async uploadMultipleDestinations(destinationsData: JsonDestinationData[]): Promise<{ success: string[], failed: Array<{ index: number, error: string }> }> {
    const success: string[] = [];
    const failed: Array<{ index: number, error: string }> = [];

    for (let i = 0; i < destinationsData.length; i++) {
      try {
        const destinationId = await this.uploadSingleDestination(destinationsData[i]);
        success.push(destinationId);
      } catch (error) {
        failed.push({
          index: i,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { success, failed };
  }

  static parseJsonString(jsonString: string): JsonDestinationData | JsonDestinationData[] {
    try {
      const parsed = JSON.parse(jsonString);
      return parsed;
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }
}