// src/utils/jsonTourUploader.ts
import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tour } from '@/types/firebase';

interface JsonTourData {
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  destinations: string[];
  activities: string[];
  accommodations: string[];
  maxGroupSize: number;
  minGroupSize: number;
  difficulty: 'easy' | 'moderate' | 'challenging';
  includes: string[];
  excludes: string[];
  images: string[];
  featured: boolean;
  status: 'active' | 'inactive' | 'draft';
  rating: number;
  reviewCount: number;
  tourType: 'standard' | 'luxury' | 'budget' | 'ultra';
  category?: string;
  accommodationLevel?: string;
  seasonality: {
    greenSeason: boolean;
    drySeason: boolean;
    bestMonths: string[];
  };
  routeMap?: {
    startPoint: { lat: number; lng: number; name: string };
    endPoint: { lat: number; lng: number; name: string };
    waypoints: Array<{
      lat: number;
      lng: number;
      name: string;
      description?: string;
      order: number;
    }>;
  };
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    accommodation: string;
    meals: string[];
    activities: Array<{
      time: string;
      activity: string;
      description: string;
      duration: string;
      location: string;
    }>;
    drivingTime: string;
    highlights: string[];
  }>;
  fitnessRequirements: {
    level: string;
    description: string;
    walkingDistance: string;
    terrain: string;
    ageRestrictions: string;
    medicalConditions: string[];
  };
  equipment: {
    provided: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
    recommended: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
    required: Array<{
      name: string;
      description: string;
      category: string;
      optional: boolean;
    }>;
  };
  groupOptions: Array<{
    type: string;
    minParticipants: number;
    maxParticipants: number;
    pricePerPerson: number;
    description: string;
  }>;
  specialFeatures: string[];
  difficultyDetails: string;
}

export class JsonTourUploader {
  static validateTourData(tourData: any): tourData is JsonTourData {
    const requiredFields = [
      'title', 'description', 'price', 'duration', 'location',
      'destinations', 'activities', 'accommodations', 'maxGroupSize',
      'minGroupSize', 'difficulty', 'includes', 'excludes', 'images',
      'featured', 'status', 'rating', 'reviewCount', 'tourType'
    ];

    for (const field of requiredFields) {
      if (!(field in tourData)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate arrays
    const arrayFields = ['destinations', 'activities', 'accommodations', 'includes', 'excludes', 'images', 'specialFeatures'];
    for (const field of arrayFields) {
      if (tourData[field] && !Array.isArray(tourData[field])) {
        throw new Error(`Field ${field} must be an array`);
      }
    }

    // Validate seasonality object
    if (!tourData.seasonality || typeof tourData.seasonality !== 'object') {
      throw new Error('Missing or invalid seasonality object');
    }

    if (!Array.isArray(tourData.seasonality.bestMonths)) {
      throw new Error('seasonality.bestMonths must be an array');
    }

    // Validate itinerary if present
    if (tourData.itinerary && !Array.isArray(tourData.itinerary)) {
      throw new Error('itinerary must be an array');
    }

    return true;
  }

  static async uploadSingleTour(tourData: JsonTourData): Promise<string> {
    try {
      // Validate the tour data
      this.validateTourData(tourData);

      // Prepare the tour data for Firestore
      const firestoreTourData = {
        ...tourData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        // Ensure all required fields have defaults
        category: tourData.category || 'Safari',
        accommodationLevel: tourData.accommodationLevel || 'Standard',
        seasonality: {
          greenSeason: tourData.seasonality?.greenSeason ?? true,
          drySeason: tourData.seasonality?.drySeason ?? true,
          bestMonths: tourData.seasonality?.bestMonths || []
        },
        itinerary: tourData.itinerary || [],
        fitnessRequirements: tourData.fitnessRequirements || {
          level: 'Easy',
          description: 'Suitable for all fitness levels',
          walkingDistance: 'Minimal',
          terrain: 'Easy',
          ageRestrictions: 'None',
          medicalConditions: []
        },
        equipment: tourData.equipment || {
          provided: [],
          recommended: [],
          required: []
        },
        groupOptions: tourData.groupOptions || [],
        specialFeatures: tourData.specialFeatures || [],
        difficultyDetails: tourData.difficultyDetails || ''
      };

      // Upload to Firestore
      const docRef = await addDoc(collection(db, 'tours'), firestoreTourData);
      return docRef.id;
    } catch (error) {
      console.error('Error uploading tour:', error);
      throw error;
    }
  }

  static async uploadMultipleTours(toursData: JsonTourData[]): Promise<{ success: string[], failed: Array<{ index: number, error: string }> }> {
    const success: string[] = [];
    const failed: Array<{ index: number, error: string }> = [];

    for (let i = 0; i < toursData.length; i++) {
      try {
        const tourId = await this.uploadSingleTour(toursData[i]);
        success.push(tourId);
      } catch (error) {
        failed.push({
          index: i,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { success, failed };
  }

  static parseJsonString(jsonString: string): JsonTourData | JsonTourData[] {
    try {
      const parsed = JSON.parse(jsonString);
      return parsed;
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }
}